import argparse
import os
import re
import shutil
import subprocess
from pathlib import Path

import yaml
from edl_reader import EDLReader

currentFile = __file__
realPath = os.path.realpath(currentFile)
dirPath = os.path.dirname(realPath)

with open("config.yml", "r") as ymlfile:
    config = yaml.safe_load(ymlfile)

def get_season(filename):
    match = re.search(
        r'''(?ix)                 # Ignore case (i), and use verbose regex (x)
        (?:                       # non-grouping pattern
          s|season|^           # e or x or episode or start of a line
          )                       # end non-grouping pattern 
        \s*                       # 0-or-more whitespaces
        (\d{1,2})                   # exactly 2 digits
        ''', filename)
    if match:
        return match.group(1)

def get_episode(filename):
    pattern = re.compile(r'''(?ix)                 # Ignore case (i), and use verbose regex (x)
        (?:                       # non-grouping pattern
          e|x|episode|^           # e or x or episode or start of a line
          )                       # end non-grouping pattern 
        \s*                       # 0-or-more whitespaces
        (\d{1,2})                   # exactly 2 digits
        ''', re.IGNORECASE)
    matches = re.findall(pattern, filename)
    match = re.search(
        r'''(?ix)                 # Ignore case (i), and use verbose regex (x)
        (?:                       # non-grouping pattern
          e|x|episode|^           # e or x or episode or start of a line
          )                       # end non-grouping pattern 
        \s*                       # 0-or-more whitespaces
        (\d{1,2})                   # exactly 2 digits
        ''', filename)
    if len(matches) > 0:
        return [i.lstrip("0") for i in matches]
    if match:
        return match.group(1)


allowed_ext = [".m4a", ".ac3", ".aac", ".eac3", ".ogg", ".opus", ".wav"]


def timecode_to_seconds(timecode):
    """Convert timecode (HH:MM:SS:FF) to seconds, assuming 25fps and 1-hour offset"""
    hours = timecode.hours
    minutes = timecode.minutes
    seconds = timecode.seconds
    frames = timecode.frames

    # Assuming 25fps for frame conversion
    fps = 25
    total_seconds = hours * 3600 + minutes * 60 + seconds + frames / fps

    # DaVinci Resolve often uses 1-hour offset (01:00:00:00 = start of video)
    # Subtract 1 hour (3600 seconds) to get actual video time
    if total_seconds >= 3600:  # Only subtract if timecode is >= 1 hour
        total_seconds -= 3600

    return total_seconds


def find_edl_file(video_file_path):
    """Find corresponding EDL file for a video file based on season and episode"""
    video_path = Path(video_file_path)
    video_filename = video_path.name
    video_dir = video_path.parent

    # Extract season and episode from video filename
    try:
        video_season = get_season(video_filename)
        video_episodes = get_episode(video_filename)

        if not video_season or not video_episodes:
            return None

        # Handle both single episode and multi-episode cases
        if isinstance(video_episodes, list):
            video_episode = video_episodes[0]  # Use first episode for matching
        else:
            video_episode = video_episodes

        video_season = video_season.lstrip("0")
        video_episode = video_episode.lstrip("0")

    except:
        return None

    # Look for EDL files with matching season and episode
    matching_edl_files = []

    for edl_file in video_dir.glob("*.edl"):
        edl_filename = edl_file.name
        try:
            edl_season = get_season(edl_filename)
            edl_episodes = get_episode(edl_filename)

            if not edl_season or not edl_episodes:
                continue

            # Handle both single episode and multi-episode cases
            if isinstance(edl_episodes, list):
                edl_episode = edl_episodes[0]  # Use first episode for matching
            else:
                edl_episode = edl_episodes

            edl_season = edl_season.lstrip("0")
            edl_episode = edl_episode.lstrip("0")

            # Check if season and episode match
            if edl_season == video_season and edl_episode == video_episode:
                matching_edl_files.append(edl_file)

        except:
            continue

    if not matching_edl_files:
        return None

    # If multiple matches, prefer the shortest filename (most specific)
    # This will prefer "S01E01.edl" over "Rocket Power_S01E01_Long_Title.edl"
    best_match = min(matching_edl_files, key=lambda x: len(x.name))
    return str(best_match)


def extract_markers_from_edl(edl_file_path):
    """Extract markers from EDL file and return sorted list of timecodes"""
    reader = EDLReader()
    edl = reader.read_file(edl_file_path)

    markers = []
    for edit in edl.edits:
        if 'marker' in edit.metadata:
            marker_time = timecode_to_seconds(edit.record_in)
            markers.append({
                'name': edit.metadata['marker'],
                'time': marker_time,
                'timecode': edit.record_in
            })

    # Sort markers by time
    markers.sort(key=lambda x: x['time'])
    return markers


def create_cut_segments(markers):
    """Create cut segments from markers (combine pairs)"""
    segments = []
    for i in range(len(markers) - 1):
        start_marker = markers[i]
        end_marker = markers[i + 1]

        segment = {
            'start_time': start_marker['time'],
            'end_time': end_marker['time'],
            'start_marker': start_marker['name'],
            'end_marker': end_marker['name'],
            'duration': end_marker['time'] - start_marker['time']
        }
        segments.append(segment)

    return segments


def clean_filename(name):
    """Clean a string to be safe for use as a filename"""
    # Replace problematic characters with underscores
    import re
    # Remove or replace characters that are problematic in filenames
    cleaned = re.sub(r'[<>:"/\\|?*]', '_', name)  # Windows forbidden chars
    cleaned = re.sub(r'\s+', '_', cleaned)  # Replace spaces with underscores
    cleaned = re.sub(r'_+', '_', cleaned)   # Replace multiple underscores with single
    cleaned = cleaned.strip('_')            # Remove leading/trailing underscores
    return cleaned


def find_keyframes_near_time(input_file, target_time, search_window=10):
    """Find keyframes near a target time"""
    try:
        # Use ffprobe to find keyframes around the target time
        start_search = max(0, target_time - search_window)
        end_search = target_time + search_window

        cmd = [
            shutil.which("ffprobe"), "-v", "quiet",
            "-select_streams", "[v:0",
            "-show_entries", "frame=pkt_pts_time,key_frame",
            "-of", "csv=p=0:nk=1",  # Added nk=1 to remove headers
            "-read_intervals", f"{start_search}%{end_search}",
            input_file
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"[warning] ffprobe failed: {result.stderr}")
            return None, None

        keyframes = []
        frame_count = 0

        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.strip().split(',')
                frame_count += 1

                # Handle different output formats
                if len(parts) >= 2:
                    try:
                        # Try to parse as pts_time,key_frame
                        pts_time_str = parts[0].strip()
                        key_frame_str = parts[1].strip()

                        # Skip lines with non-numeric data
                        if not pts_time_str or not key_frame_str:
                            continue

                        pts_time = float(pts_time_str)
                        is_keyframe = int(key_frame_str)

                        if is_keyframe == 1:
                            keyframes.append(pts_time)

                    except (ValueError, IndexError) as e:
                        # Skip malformed lines
                        continue



        if not keyframes:
            # Fallback: assume keyframes every 2 seconds (common for video)
            print(f"[warning] No keyframes detected, using estimated keyframe positions")
            estimated_keyframes = []
            keyframe_interval = 2.0  # Assume 2-second keyframe interval

            start_kf = int(start_search / keyframe_interval) * keyframe_interval
            current_kf = start_kf

            while current_kf <= end_search:
                if current_kf >= start_search:
                    estimated_keyframes.append(current_kf)
                current_kf += keyframe_interval

            keyframes = estimated_keyframes

        if not keyframes:
            return None, None

        # Find closest keyframes before and after target time
        before_keyframes = [kf for kf in keyframes if kf <= target_time]
        after_keyframes = [kf for kf in keyframes if kf > target_time]

        closest_before = max(before_keyframes) if before_keyframes else None
        closest_after = min(after_keyframes) if after_keyframes else None

        return closest_before, closest_after

    except Exception as e:
        print(f"[warning] Error finding keyframes: {e}")
        return None, None


def concatenate_with_filter_complex(segments, output_file):
    """Alternative concatenation method using filter_complex"""
    try:
        print(f"[info] Using filter_complex concatenation for {len(segments)} segments")

        # Build ffmpeg command with multiple inputs and filter_complex
        cmd = [shutil.which("ffmpeg"), "-y"]

        # Add all input files
        for segment in segments:
            cmd.extend(["-i", segment])

        # Build filter_complex for concatenation (video and audio only for filter_complex)
        # Note: filter_complex doesn't handle subtitles well, so this is a fallback method
        if len(segments) == 2:
            filter_complex = "[0:v][0:a][1:v][1:a]concat=n=2:v=1:a=1[outv][outa]"
        elif len(segments) == 3:
            filter_complex = "[0:v][0:a][1:v][1:a][2:v][2:a]concat=n=3:v=1:a=1[outv][outa]"
        else:
            # For more segments, build dynamically
            video_inputs = "".join([f"[{i}:v]" for i in range(len(segments))])
            audio_inputs = "".join([f"[{i}:a]" for i in range(len(segments))])
            filter_complex = f"{video_inputs}{audio_inputs}concat=n={len(segments)}:v=1:a=1[outv][outa]"

        cmd.extend([
            "-filter_complex", filter_complex,
            "-map", "[outv]",
            "-map", "[outa]",
            "-c:v", "copy",
            "-c:a", "copy",
            output_file
        ])

        print(f"[warning] Using filter_complex fallback - subtitles may not be preserved")

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            if os.path.exists(output_file):
                return True

        print(f"[error] Filter complex concatenation failed")
        return False

    except Exception as e:
        print(f"[error] Error in filter complex concatenation: {e}")
        return False


def get_video_codec(input_file):
    """Get the video codec of the input file"""
    try:
        cmd = [
            shutil.which("ffprobe"), "-v", "quiet",
            "-select_streams", "v:0",
            "-show_entries", "stream=codec_name",
            "-of", "csv=p=0",
            input_file
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            codec = result.stdout.strip()
            return codec
        return "libx264"  # fallback
    except:
        return "libx264"  # fallback


def smart_cut_with_segments(input_file, output_file, start_time, end_time,
                           start_kf_before, start_kf_after, end_kf_before, end_kf_after, shift_start_times=True):
    """
    Perform true smart cutting by segmenting the cut:
    1. Re-encode small portion from exact start to next keyframe
    2. Stream copy main portion between keyframes
    3. Re-encode small portion from last keyframe to exact end
    4. Concatenate all segments
    """
    try:
        import tempfile
        import os

        temp_dir = tempfile.mkdtemp()
        segments = []

        # Detect original video codec to maintain consistency
        original_codec = get_video_codec(input_file)
        print(f"[info] Original video codec: {original_codec}")

        # Map codec to encoder
        if original_codec == "hevc":
            video_encoder = "libx265"
            video_preset = "fast"
            video_crf = "23"  # Slightly higher CRF for HEVC
        else:
            video_encoder = "libx264"
            video_preset = "fast"
            video_crf = "18"

        print(f"[info] Using encoder: {video_encoder}")
        print(f"[info] Shift start times: {shift_start_times}")
        print(f"[info] True smart cutting: segmented approach")

        # Prepare avoid_negative_ts arguments
        avoid_negative_ts_args = ["-avoid_negative_ts", "make_zero"] if shift_start_times else []

        # When keyframe detection fails, use estimated keyframes for smart cutting
        if start_kf_after is None or end_kf_before is None:
            print(f"[info] Keyframe detection failed, using estimated positions for smart cutting")
            # Use estimated keyframe positions (every 2 seconds)
            keyframe_interval = 2.0

            if start_kf_after is None:
                # Find next estimated keyframe after start_time
                start_kf_after = ((int(start_time / keyframe_interval) + 1) * keyframe_interval)

            if end_kf_before is None:
                # Find previous estimated keyframe before end_time
                end_kf_before = (int(end_time / keyframe_interval) * keyframe_interval)

        # Determine cutting strategy based on keyframe positions
        tolerance = 1.0  # 1 second tolerance for "close enough"

        start_needs_reenc = abs(start_time - start_kf_after) > tolerance
        end_needs_reenc = abs(end_time - end_kf_before) > tolerance

        if not start_needs_reenc and not end_needs_reenc:
            # Both cut points are close to keyframes - simple stream copy
            duration = end_kf_before - start_kf_after

            print(f"[info] Simple stream copy from {start_kf_after:.2f}s to {end_kf_before:.2f}s")
            cmd = [
                shutil.which("ffmpeg"), "-y",
                "-ss", str(start_kf_after),
                "-i", input_file,
                "-t", str(duration),
                "-c", "copy",  # Copy all streams (video, audio, subtitles)
                "-map", "0",   # Map all streams from input
                "-avoid_negative_ts", "make_zero",
                output_file
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        # Complex smart cutting with segments
        current_time = start_time
        segment_num = 0

        # Segment 1: Exact start to next keyframe (re-encode if needed)
        if start_kf_after and start_kf_after > start_time:
            segment_num += 1
            segment_file = os.path.join(temp_dir, f"segment_{segment_num:02d}.mkv")
            segment_duration = start_kf_after - start_time

            print(f"[info] Segment {segment_num}: Re-encode {start_time:.2f}s to {start_kf_after:.2f}s ({segment_duration:.2f}s)")

            cmd = [
                shutil.which("ffmpeg"), "-y",
                "-ss", str(start_time),
                "-i", input_file,
                "-t", str(segment_duration),
                "-c:v", video_encoder, "-preset", video_preset, "-crf", video_crf,
                "-c:a", "copy",  # Keep all audio streams
                "-c:s", "copy",  # Keep all subtitle streams
                "-map", "0",     # Map all streams from input
                "-fflags", "+genpts"  # Generate presentation timestamps
            ] + avoid_negative_ts_args + [segment_file]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"[error] Failed to create segment {segment_num}: {result.stderr}")
                return False

            # Verify segment was created
            if os.path.exists(segment_file):
                segments.append(segment_file)
            else:
                print(f"[error] Segment {segment_num} file not created")
                return False

            current_time = start_kf_after

        # Segment 2: Main portion between keyframes (stream copy)
        if end_kf_before and end_kf_before > current_time:
            segment_num += 1
            segment_file = os.path.join(temp_dir, f"segment_{segment_num:02d}.mkv")
            segment_duration = end_kf_before - current_time

            print(f"[info] Segment {segment_num}: Stream copy {current_time:.2f}s to {end_kf_before:.2f}s ({segment_duration:.2f}s)")

            cmd = [
                shutil.which("ffmpeg"), "-y",
                "-ss", str(current_time),
                "-i", input_file,
                "-t", str(segment_duration),
                "-c", "copy",  # Copy all streams (video, audio, subtitles)
                "-map", "0",   # Map all streams from input
                "-fflags", "+genpts"  # Generate presentation timestamps
            ] + avoid_negative_ts_args + [segment_file]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"[error] Failed to create segment {segment_num}: {result.stderr}")
                return False

            # Verify segment was created
            if os.path.exists(segment_file):
                segments.append(segment_file)
            else:
                print(f"[error] Segment {segment_num} file not created")
                return False

            current_time = end_kf_before

        # Segment 3: Last keyframe to exact end (re-encode if needed)
        if current_time < end_time:
            segment_num += 1
            segment_file = os.path.join(temp_dir, f"segment_{segment_num:02d}.mkv")
            segment_duration = end_time - current_time

            print(f"[info] Segment {segment_num}: Re-encode {current_time:.2f}s to {end_time:.2f}s ({segment_duration:.2f}s)")

            cmd = [
                shutil.which("ffmpeg"), "-y",
                "-ss", str(current_time),
                "-i", input_file,
                "-t", str(segment_duration),
                "-c:v", video_encoder, "-preset", video_preset, "-crf", video_crf,
                "-c:a", "copy",  # Keep all audio streams
                "-c:s", "copy",  # Keep all subtitle streams
                "-map", "0",     # Map all streams from input
                "-fflags", "+genpts"  # Generate presentation timestamps
            ] + avoid_negative_ts_args + [segment_file]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"[error] Failed to create segment {segment_num}: {result.stderr}")
                return False

            # Verify segment was created
            if os.path.exists(segment_file):
                segments.append(segment_file)
            else:
                print(f"[error] Segment {segment_num} file not created")
                return False

        # Concatenate segments if we have multiple
        if len(segments) == 0:
            print(f"[error] No segments created")
            return False
        elif len(segments) == 1:
            # Only one segment, just move it
            print(f"[info] Single segment, moving to output")
            shutil.move(segments[0], output_file)



        elif len(segments) > 1:
            # Multiple segments, concatenate them
            print(f"[info] Concatenating {len(segments)} segments")

            # Verify all segments exist
            for i, segment in enumerate(segments, 1):
                if not os.path.exists(segment):
                    print(f"[error] Segment {i} missing: {segment}")
                    return False

            # Create concat file list
            concat_file = os.path.join(temp_dir, "concat_list.txt")
            with open(concat_file, 'w') as f:
                for segment in segments:
                    # Use absolute paths for Windows compatibility
                    abs_path = os.path.abspath(segment).replace('\\', '/')
                    f.write(f"file '{abs_path}'\n")



            # Concatenate with stream copy since all segments have same codec
            cmd = [
                shutil.which("ffmpeg"), "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", concat_file,
                "-c", "copy",  # Copy all streams (video, audio, subtitles)
                "-map", "0",   # Map all streams from input
                output_file
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"[error] Failed to concatenate segments (return code: {result.returncode})")

                # Try alternative concatenation method
                print(f"[info] Trying alternative concatenation method...")
                return concatenate_with_filter_complex(segments, output_file)
            else:
                print(f"[info] Concatenation completed with return code 0")

            # Verify final output
            if not os.path.exists(output_file):
                print(f"[error] Final output file not created")
                return False

        # Cleanup temp directory
        import shutil as shutil_module
        shutil_module.rmtree(temp_dir)

        print(f"[info] Smart cut completed successfully")
        return True

    except Exception as e:
        print(f"[error] Error during smart cutting: {e}")
        return False


def smart_cut_video(input_file, output_file, start_time, end_time, exact_cutting=True, shift_start_times=True, shift_frames=8):
    """
    Perform smart cut using keyframe analysis
    - exact_cutting=True: Always cut at exact times (re-encode for accuracy)
    - exact_cutting=False: Align to keyframes when possible (faster, less precise)
    - shift_start_times=True: Use avoid_negative_ts make_zero (like LosslessCut default)
    - shift_frames: Number of frames to shift start times (like LosslessCut's frame shift setting)
    """
    try:
        duration = end_time - start_time
        print(f"[info] Smart cutting from {start_time:.2f}s to {end_time:.2f}s (duration: {duration:.2f}s)")

        # Find keyframes near start and end points
        print(f"[info] Analyzing keyframes near cut points...")
        start_kf_before, start_kf_after = find_keyframes_near_time(input_file, start_time)
        end_kf_before, end_kf_after = find_keyframes_near_time(input_file, end_time)

        if exact_cutting:
            # True smart cutting: re-encode only small portions around exact cut points
            return smart_cut_with_segments(input_file, output_file, start_time, end_time,
                                         start_kf_before, start_kf_after, end_kf_before, end_kf_after, shift_start_times)
        else:
            # Use keyframe-aligned cutting for speed (less precise)
            start_tolerance = 2.0  # seconds
            end_tolerance = 2.0    # seconds

            start_exact = start_kf_before is None or abs(start_time - start_kf_before) > start_tolerance
            end_exact = end_kf_after is None or abs(end_time - end_kf_after) > end_tolerance

            if not start_exact and not end_exact:
                # Both cut points are close to keyframes - use stream copy
                actual_start = start_kf_before if start_kf_before else start_time
                actual_end = end_kf_after if end_kf_after else end_time
                actual_duration = actual_end - actual_start

                print(f"[info] Using fast stream copy (keyframes at {actual_start:.2f}s and {actual_end:.2f}s)")
                cmd = [
                    shutil.which("ffmpeg"), "-y",
                    "-ss", str(actual_start),
                    "-i", input_file,
                    "-t", str(actual_duration),
                    "-c", "copy",  # Copy all streams (video, audio, subtitles)
                    "-map", "0",   # Map all streams from input
                    "-avoid_negative_ts", "make_zero",
                    output_file
                ]
            else:
                # Need exact cutting - re-encode
                print(f"[info] Using exact cutting (re-encode required)")
                cmd = [
                    shutil.which("ffmpeg"), "-y",
                    "-ss", str(start_time),
                    "-i", input_file,
                    "-t", str(duration),
                    "-c:v", "libx264", "-preset", "fast", "-crf", "18",
                    "-c:a", "copy",  # Keep all audio streams
                    "-c:s", "copy",  # Keep all subtitle streams
                    "-map", "0",     # Map all streams from input
                    "-avoid_negative_ts", "make_zero",
                    output_file
                ]

        # Execute the cutting command
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"[info] Smart cut successful: {output_file}")
            return True
        else:
            print(f"[error] Smart cut failed: {result.stderr}")
            return False

    except Exception as e:
        print(f"[error] Error during smart cutting: {e}")
        return False


def process_edl_cuts(video_file_path, edl_file_path, output_dir, exact_cutting=True, shift_start_times=True):
    """Process EDL file and create cut segments"""
    print(f"[info] Processing EDL cuts for {video_file_path}")
    print(f"[info] Cutting mode: {'Exact (re-encode)' if exact_cutting else 'Fast (keyframe-aligned)'}")
    print(f"[info] Shift start times: {shift_start_times}")

    # Extract markers from EDL
    markers = extract_markers_from_edl(edl_file_path)
    if len(markers) < 2:
        print(f"[warning] Need at least 2 markers for cutting, found {len(markers)}")
        return []

    print(f"[info] Found {len(markers)} markers, will create {len(markers)-1} segments")

    # Create cut segments
    segments = create_cut_segments(markers)

    # Generate output files
    video_path = Path(video_file_path)
    base_name = video_path.stem
    extension = video_path.suffix

    output_files = []

    for i, segment in enumerate(segments, 1):
        # Create filename using start marker name
        start_marker_clean = clean_filename(segment['start_marker'])

        output_filename = f"{start_marker_clean}{extension}"
        output_file_path = os.path.join(output_dir, output_filename)

        print(f"[info] Creating segment {i}: {segment['start_marker']} -> {segment['end_marker']}")
        print(f"[info] Time range: {segment['start_time']:.2f}s - {segment['end_time']:.2f}s ({segment['duration']:.2f}s)")

        # Perform the cut
        if smart_cut_video(video_file_path, output_file_path, segment['start_time'], segment['end_time'], exact_cutting, shift_start_times):
            output_files.append(output_file_path)
            print(f"[success] Created: {output_filename}")
        else:
            print(f"[error] Failed to create: {output_filename}")

    return output_files


def run_edl_only(args):
    """Run EDL cutting only without audio processing"""
    if args.dir is not None:
        args.dir = args.dir.replace("\\", "/")
    else:
        args.dir = config['dub_path'].replace("\\", "/")

    directory = os.fsencode(args.dir)
    cut_output_dir = os.path.join(args.dir, "edl_cuts")
    edl_files_found = False

    for file in os.listdir(directory):
        filename = os.fsdecode(file)
        if "._" not in filename and filename.endswith(".mkv"):
            video_path = os.path.join(args.dir, filename)
            edl_file = find_edl_file(video_path)

            if edl_file:
                # Create output directory if this is the first EDL file found
                if not edl_files_found:
                    os.makedirs(cut_output_dir, exist_ok=True)
                    print(f"[info] EDL cut output directory: {cut_output_dir}")
                    edl_files_found = True

                print(f"[info] Processing {filename} with matching EDL: {os.path.basename(edl_file)}")
                cut_files = process_edl_cuts(video_path, edl_file, cut_output_dir, exact_cutting=args.fast_cut == False, shift_start_times=not args.no_shift_start_times)
                if cut_files:
                    print(f"[info] Created {len(cut_files)} cut segments for {filename}")
                else:
                    print(f"[warning] No cut segments created for {filename}")
            else:
                print(f"[info] No matching EDL file found for {filename}, skipping")

    if not edl_files_found:
        print("[info] No EDL files found matching any video files in the directory")


def run(args):
    if args.dir is not None:
        args.dir = args.dir.replace("\\", "/")
    else:
        args.dir = config['dub_path'].replace("\\", "/")

    directory = os.fsencode(args.dir)

    # Create output directory for cut segments (will be used if EDL files are found)
    cut_output_dir = os.path.join(args.dir, "edl_cuts")
    edl_cut_enabled = False

    for file in os.listdir(directory):
        filename = os.fsdecode(file)
        basename = os.path.splitext(filename)[0]
        if "._" not in filename and filename.endswith(".mkv") and "dub" not in filename:
            try:
                season = get_season(filename).lstrip("0")
                episode = get_episode(filename)
            except:
                continue
            for a_file in os.listdir(directory):
                a_filename = os.fsdecode(a_file)
                a_basename = os.path.splitext(a_filename)[0]
                a_ext = os.path.splitext(a_filename)[-1]
                if "._" not in a_filename and a_ext in allowed_ext and 'dub' not in a_filename and a_basename != basename:
                    try:
                        a_season = get_season(a_filename).lstrip("0")
                        a_episode = get_episode(a_filename)[0]
                    except:
                        continue
                    if a_season is not None and a_season == season and a_episode is not None and a_episode in episode:
                        if args.multi:
                            if episode.index(a_episode) == 0:
                                if "002" not in basename:
                                    continue
                            elif episode.index(a_episode) == 1:
                                if "003" not in basename:
                                    continue
                        if args.fix_duration:
                            print(f"[info] Fixing duration for {a_filename}")
                            ffprobe_output = subprocess.run([shutil.which("ffprobe"), f"{args.dir}/{filename}"], capture_output=True)
                            matches = re.findall("Duration: (.*), start: 0.000000", ffprobe_output.stderr.decode("utf-8"))
                            base, ext = os.path.splitext(a_filename)
                            subprocess.run([shutil.which('ffmpeg'), "-i", f'{args.dir}/{a_filename}', '-t', matches[0], '-c', 'copy',  f'{args.dir}/{a_filename}.v2.{ext}'])
                            os.remove(f'{args.dir}/{a_filename}')
                            os.rename(f'{args.dir}/{a_filename}.v2.{ext}', f'{args.dir}/{a_filename}')

                        if basename in a_filename:
                            os.remove(f"{args.dir}/{a_filename}")
                            continue

                        if a_ext == ".wav":
                            subprocess.run([shutil.which("ffmpeg"), "-i", f"{args.dir}/{a_filename}",
                                            f"{args.dir}/{a_basename}.ac3"])
                            os.remove(f"{args.dir}/{a_filename}")
                            a_filename = f"{a_basename}.ac3"

                        subprocess.run([shutil.which("mkvmerge"), "-o", f"{args.dir}/{basename}.v2", "--default-track",
                                        "1:no", f"{args.dir}/{filename}",
                                        "--language", "0:nld",
                                        '--track-name', f'0:Nederlands',
                                        f"{args.dir}/{a_filename}"])
                        if os.path.exists(args.dir + "/" + basename + ".v2"):
                            print("[info] Successful added audio to " + filename)
                            os.remove(args.dir + "/" + filename)
                            os.remove(args.dir + "/" + a_filename)
                            final_video_path = args.dir + "/" + filename
                            os.rename(args.dir + "/" + basename + ".v2", final_video_path)

                            # Check for EDL file and process cuts automatically
                            edl_file = find_edl_file(final_video_path)
                            if edl_file:
                                # Create output directory if this is the first EDL file found
                                if not edl_cut_enabled:
                                    os.makedirs(cut_output_dir, exist_ok=True)
                                    print(f"[info] EDL cut output directory: {cut_output_dir}")
                                    edl_cut_enabled = True

                                print(f"[info] Found EDL file: {edl_file}")
                                cut_files = process_edl_cuts(final_video_path, edl_file, cut_output_dir, exact_cutting=True, shift_start_times=not args.no_shift_start_times)
                                if cut_files:
                                    print(f"[info] Created {len(cut_files)} cut segments")
                                else:
                                    print("[warning] No cut segments were created")
                            else:
                                print(f"[info] No matching EDL file found for {filename}")
                        break
        else:
            continue


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dir", help="Directory to use. If not set it will use dub_path from config", required=False, type=str, default=None)
    parser.add_argument("--multi", action='store_true')
    parser.add_argument("--fix-duration", help="Fix the duration of file", action='store_true')
    parser.add_argument("--edl-only", help="Only perform EDL cutting without audio processing", action='store_true')
    parser.add_argument("--fast-cut", help="Use fast keyframe-aligned cutting (less precise but faster)", action='store_true')
    parser.add_argument("--no-shift-start-times", help="Disable shifting start times to zero (like LosslessCut's 'Shift start times' option)", action='store_true')
    args = parser.parse_args()

    if args.edl_only:
        run_edl_only(args)
    else:
        run(args)

if __name__ == "__main__":
    main()