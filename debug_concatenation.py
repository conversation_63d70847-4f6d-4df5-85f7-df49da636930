#!/usr/bin/env python3
"""
Debug concatenation issues in smart cutting
"""

import subprocess
import shutil
import tempfile
import os
from pathlib import Path


def analyze_video_properties(video_file):
    """Analyze video properties using ffprobe"""
    print(f"\n=== Analyzing {os.path.basename(video_file)} ===")
    
    cmd = [
        shutil.which("ffprobe"), "-v", "quiet",
        "-show_streams", "-show_format",
        "-of", "json",
        video_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            
            # Video stream info
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    print(f"Video: {stream.get('codec_name')} {stream.get('width')}x{stream.get('height')}")
                    print(f"  Frame rate: {stream.get('r_frame_rate')}")
                    print(f"  Pixel format: {stream.get('pix_fmt')}")
                    print(f"  Profile: {stream.get('profile')}")
                    print(f"  Level: {stream.get('level')}")
                elif stream.get('codec_type') == 'audio':
                    print(f"Audio: {stream.get('codec_name')} {stream.get('sample_rate')}Hz")
                    print(f"  Channels: {stream.get('channels')}")
                    print(f"  Bitrate: {stream.get('bit_rate')}")
            
            # Format info
            format_info = data.get('format', {})
            duration = float(format_info.get('duration', 0))
            print(f"Duration: {duration:.2f}s ({duration/60:.1f} min)")
            size_mb = int(format_info.get('size', 0)) / (1024 * 1024)
            print(f"Size: {size_mb:.1f}MB")
            
            return data
        else:
            print(f"Error: {result.stderr}")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None


def create_test_segments():
    """Create test segments to debug concatenation"""
    print("\n=== Creating Test Segments ===")
    
    # Find a video file to test with
    test_dir = "C:/Users/<USER>/Videos/Rocket Power/test/"
    video_files = []
    
    if os.path.exists(test_dir):
        for file in os.listdir(test_dir):
            if file.endswith('.mkv') and '._' not in file:
                video_files.append(os.path.join(test_dir, file))
    
    if not video_files:
        print("No video files found for testing")
        return
    
    input_file = video_files[0]
    print(f"Using: {os.path.basename(input_file)}")
    
    # Create temp directory
    temp_dir = tempfile.mkdtemp()
    print(f"Temp dir: {temp_dir}")
    
    # Create 3 test segments similar to what smart cutting does
    segments = []
    
    # Segment 1: Re-encode small portion (0-2 seconds)
    print("\nCreating segment 1 (re-encode)...")
    seg1 = os.path.join(temp_dir, "segment_01.mkv")
    cmd1 = [
        shutil.which("ffmpeg"), "-y",
        "-ss", "0",
        "-i", input_file,
        "-t", "2",
        "-c:v", "libx264", "-preset", "fast", "-crf", "18",
        "-c:a", "aac", "-b:a", "192k",
        "-avoid_negative_ts", "make_zero",
        "-fflags", "+genpts",
        seg1
    ]
    
    result = subprocess.run(cmd1, capture_output=True, text=True)
    if result.returncode == 0:
        segments.append(seg1)
        analyze_video_properties(seg1)
    else:
        print(f"Failed to create segment 1: {result.stderr}")
        return
    
    # Segment 2: Stream copy main portion (2-10 seconds)
    print("\nCreating segment 2 (stream copy)...")
    seg2 = os.path.join(temp_dir, "segment_02.mkv")
    cmd2 = [
        shutil.which("ffmpeg"), "-y",
        "-ss", "2",
        "-i", input_file,
        "-t", "8",
        "-c", "copy",
        "-avoid_negative_ts", "make_zero",
        "-fflags", "+genpts",
        seg2
    ]
    
    result = subprocess.run(cmd2, capture_output=True, text=True)
    if result.returncode == 0:
        segments.append(seg2)
        analyze_video_properties(seg2)
    else:
        print(f"Failed to create segment 2: {result.stderr}")
        return
    
    # Segment 3: Re-encode small portion (10-12 seconds)
    print("\nCreating segment 3 (re-encode)...")
    seg3 = os.path.join(temp_dir, "segment_03.mkv")
    cmd3 = [
        shutil.which("ffmpeg"), "-y",
        "-ss", "10",
        "-i", input_file,
        "-t", "2",
        "-c:v", "libx264", "-preset", "fast", "-crf", "18",
        "-c:a", "aac", "-b:a", "192k",
        "-avoid_negative_ts", "make_zero",
        "-fflags", "+genpts",
        seg3
    ]
    
    result = subprocess.run(cmd3, capture_output=True, text=True)
    if result.returncode == 0:
        segments.append(seg3)
        analyze_video_properties(seg3)
    else:
        print(f"Failed to create segment 3: {result.stderr}")
        return
    
    # Test concatenation
    print("\n=== Testing Concatenation ===")
    
    # Method 1: concat demuxer
    print("\nMethod 1: concat demuxer")
    concat_file = os.path.join(temp_dir, "concat_list.txt")
    with open(concat_file, 'w') as f:
        for segment in segments:
            abs_path = os.path.abspath(segment).replace('\\', '/')
            f.write(f"file '{abs_path}'\n")
    
    output1 = os.path.join(temp_dir, "output_concat.mkv")
    cmd_concat = [
        shutil.which("ffmpeg"), "-y",
        "-f", "concat",
        "-safe", "0",
        "-i", concat_file,
        "-c", "copy",
        output1
    ]
    
    print(f"Command: {' '.join(cmd_concat)}")
    result = subprocess.run(cmd_concat, capture_output=True, text=True)
    print(f"Return code: {result.returncode}")
    if result.stdout:
        print(f"Stdout: {result.stdout}")
    if result.stderr:
        print(f"Stderr: {result.stderr}")
    
    if os.path.exists(output1):
        analyze_video_properties(output1)
    
    # Method 2: filter_complex
    print("\nMethod 2: filter_complex")
    output2 = os.path.join(temp_dir, "output_filter.mkv")
    cmd_filter = [
        shutil.which("ffmpeg"), "-y",
        "-i", seg1,
        "-i", seg2, 
        "-i", seg3,
        "-filter_complex", "[0:v][0:a][1:v][1:a][2:v][2:a]concat=n=3:v=1:a=1[outv][outa]",
        "-map", "[outv]",
        "-map", "[outa]",
        "-c:v", "copy",
        "-c:a", "copy",
        output2
    ]
    
    print(f"Command: {' '.join(cmd_filter)}")
    result = subprocess.run(cmd_filter, capture_output=True, text=True)
    print(f"Return code: {result.returncode}")
    if result.stdout:
        print(f"Stdout: {result.stdout}")
    if result.stderr:
        print(f"Stderr: {result.stderr}")
    
    if os.path.exists(output2):
        analyze_video_properties(output2)
    
    # Cleanup
    import shutil as shutil_module
    shutil_module.rmtree(temp_dir)
    
    return segments


def main():
    create_test_segments()


if __name__ == "__main__":
    main()
