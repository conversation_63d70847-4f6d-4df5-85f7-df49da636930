import argparse
import os
import shutil
import subprocess

import questionary
import yaml
from pick import pick
from pymediainfo import MediaInfo
from videoprops import get_video_properties

with open("../config/config.yml", "r") as ymlfile:
    config = yaml.safe_load(ymlfile)

ffmpeg = shutil.which("ffmpeg")
mkvmerge = shutil.which("mkvmerge")

def run(args):
    if args.dir is not None:
        args.dir = args.dir.replace("\\", "/")
    else:
        args.dir = config['dub_path'].replace("\\", "/")

    directory = os.fsencode(args.dir)

    from_fps = float(questionary.select("From FPS?", choices=["23.976","24","25","29.97","30"]).ask())
    to_fps = float(questionary.select("To FPS?", choices=["23.976","24","25","29.97","30"]).ask())
    output_fps = float(questionary.select("Output FPS?", choices=["23.976","24","25","29.97","30"]).ask())

    files = []
    for file in os.listdir(directory):
        files.append(os.fsdecode(file))

    entries = pick(files, title="Which files to convert?", multiselect=True)

    for filename in entries:
        filename = files[filename[1]]
        basename = os.path.splitext(filename)[0]
        if (filename.endswith(".mkv") or filename.endswith(".mp4")):
            print(f"[info] Checking {filename}")
            audio_format = "wav"

            media_info = MediaInfo.parse(f'{args.dir}/{filename}',
                                         library_file="/opt/homebrew/Cellar/libmediainfo/25.04/lib/libmediainfo.dylib")

            # if abs(frame_rate - args.to_fps) < 0.1:
            #     print(f'[info] Skipping {filename} since frame rate already correct ({frame_rate} fps)')
            #     continue
            # else:
            #     print(f'[info] Processing {filename} ({frame_rate} to {args.to_fps} fps)')

            # Let's convert the audio including pitch correction
            tempo = to_fps / from_fps
            for audio_track in media_info.audio_tracks:
                command = [ffmpeg, '-hide_banner', '-loglevel', 'error', '-i', f"{args.dir}/{filename}",
                           '-map', f'0:a:{audio_track.stream_identifier}', '-filter:a', f"atempo={tempo}", '-vn',
                           f'{args.dir}/{basename}.{audio_track.stream_identifier}.{audio_format}']
                subprocess.run(command)

            # Start creating the command to speed up video and add converted audio tracks
            subprocess.run([f'{shutil.which("ffmpeg")}', '-i', f"{args.dir}/{filename}",
                            "-r", str(output_fps),
                            "-an",
                            "-filter:v", f"setpts={str(from_fps / to_fps)}*PTS",
                            f"{args.dir}/{basename}.v2.mkv"])

            os.remove(f"{args.dir}/{filename}")

            command = [mkvmerge, '-o', f"{args.dir}/{filename}", f"{args.dir}/{basename}.v2.mkv"]

            # Add the speed-up audio track
            for audio_track in media_info.audio_tracks:
                # Set language of track
                if audio_track.language:
                    language = audio_track.language
                else:
                    language = "en"
                if audio_track.delay == None:
                    delay = 0
                else :
                    delay = audio_track.delay
                command += ["--language", f"0:{language}", "--sync", f"0:{delay}"]

                # Add title to track if set
                if audio_track.title:
                    command += ["--track-name", f"0:{audio_track.title}"]

                command += [f"{args.dir}/{basename}.{audio_track.stream_identifier}.{audio_format}"]

            subprocess.run(command)

            # Remove unwanted files
            for audio_track in media_info.audio_tracks:
                os.remove(f'{args.dir}/{basename}.v2.mkv')
                os.remove(f'{args.dir}/{basename}.{audio_track.stream_identifier}.{audio_format}')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dir", help="Directory to use. If not set it will use dub_path from config", required=False, type=str, default=None)
    parser.add_argument("--audio-ext", help="The extension of the audio file", default="aac", type=str)
    parser.add_argument("--video-ext", help="The extension of the video file", required=False, type=str)
    parser.add_argument("--sub-tracks", help="The subtitle track identifiers", nargs="+", default=[])
    parser.add_argument("--audio-tracks", help="The subtitle track identifiers", nargs="+", default=[])
    parser.add_argument("--only-audio", help="Whether to only speed up audio and not mux", default=False, type=bool)
    parser.add_argument("--from-fps", help="The extension of the audio file", required=False, default=None, type=float)
    parser.add_argument("--to-fps", help="The extension of the audio file", required=False, default=25, type=float)
    parser.add_argument("--new-fps", help="The fps of the output file", required=False, default=25, type=float)
    args = parser.parse_args()
    run(args)

if __name__ == "__main__":
    main()
