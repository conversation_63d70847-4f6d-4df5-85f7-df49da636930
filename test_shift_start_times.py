#!/usr/bin/env python3
"""
Test the effect of avoid_negative_ts settings on concatenation
"""

import subprocess
import shutil
import tempfile
import os
from pathlib import Path


def analyze_timestamps(video_file):
    """Analyze timestamps in a video file"""
    print(f"\n=== Analyzing timestamps in {os.path.basename(video_file)} ===")
    
    cmd = [
        shutil.which("ffprobe"), "-v", "quiet",
        "-show_entries", "format=start_time,duration",
        "-show_entries", "stream=start_time,duration",
        "-of", "csv=p=0",
        video_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if line:
                    if i == 0:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            print(f"Format - Start: {parts[0]}, Duration: {parts[1]}")
                    else:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            print(f"Stream {i-1} - Start: {parts[0]}, Duration: {parts[1]}")
        else:
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"Error: {e}")


def test_avoid_negative_ts_settings():
    """Test different avoid_negative_ts settings"""
    print("\n=== Testing avoid_negative_ts Settings ===")
    
    # Find a video file to test with
    test_dir = "C:/Users/<USER>/Videos/Rocket Power/test/"
    video_files = []
    
    if os.path.exists(test_dir):
        for file in os.listdir(test_dir):
            if file.endswith('.mkv') and '._' not in file:
                video_files.append(os.path.join(test_dir, file))
    
    if not video_files:
        print("No video files found for testing")
        return
    
    input_file = video_files[0]
    print(f"Using: {os.path.basename(input_file)}")
    
    # Create temp directory
    temp_dir = tempfile.mkdtemp()
    print(f"Temp dir: {temp_dir}")
    
    # Test different avoid_negative_ts settings
    settings = [
        ("disabled", []),
        ("make_zero", ["-avoid_negative_ts", "make_zero"]),
        ("make_non_negative", ["-avoid_negative_ts", "make_non_negative"]),
    ]
    
    segments = []
    
    for setting_name, avoid_negative_ts_args in settings:
        print(f"\n--- Testing {setting_name} ---")
        
        # Create a segment from middle of video (where timestamps might be non-zero)
        output_file = os.path.join(temp_dir, f"segment_{setting_name}.mkv")
        
        cmd = [
            shutil.which("ffmpeg"), "-y",
            "-ss", "300",  # Start at 5 minutes
            "-i", input_file,
            "-t", "10",    # 10 seconds
            "-c", "copy"
        ] + avoid_negative_ts_args + [output_file]
        
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Created {setting_name} segment")
            analyze_timestamps(output_file)
            segments.append((setting_name, output_file))
        else:
            print(f"❌ Failed to create {setting_name} segment: {result.stderr}")
    
    # Test concatenation of segments with different timestamp behaviors
    if len(segments) >= 2:
        print(f"\n=== Testing Concatenation ===")
        
        # Try concatenating segments with different timestamp settings
        concat_file = os.path.join(temp_dir, "concat_list.txt")
        with open(concat_file, 'w') as f:
            for setting_name, segment_file in segments[:2]:  # Use first 2 segments
                abs_path = os.path.abspath(segment_file).replace('\\', '/')
                f.write(f"file '{abs_path}'\n")
        
        output_concat = os.path.join(temp_dir, "concatenated.mkv")
        cmd_concat = [
            shutil.which("ffmpeg"), "-y",
            "-f", "concat",
            "-safe", "0",
            "-i", concat_file,
            "-c", "copy",
            output_concat
        ]
        
        print(f"Concat command: {' '.join(cmd_concat)}")
        result = subprocess.run(cmd_concat, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Concatenation successful")
            analyze_timestamps(output_concat)
        else:
            print(f"❌ Concatenation failed: {result.stderr}")
    
    # Cleanup
    import shutil as shutil_module
    shutil_module.rmtree(temp_dir)


def test_smart_cut_timestamps():
    """Test timestamps in our smart cut segments"""
    print("\n=== Testing Smart Cut Timestamp Behavior ===")
    
    # This would test our actual smart cutting implementation
    # to see if the timestamps are properly aligned for concatenation
    
    from append_audio import extract_markers_from_edl, create_cut_segments
    
    edl_file = "S01E01.edl"
    if not os.path.exists(edl_file):
        print("EDL file not found")
        return
    
    markers = extract_markers_from_edl(edl_file)
    if len(markers) < 2:
        print("Not enough markers")
        return
    
    segments = create_cut_segments(markers)
    if len(segments) < 1:
        print("No segments created")
        return
    
    # Analyze the second segment (the one that was having concatenation issues)
    segment = segments[1]  # Marker 2 -> Marker 3
    
    print(f"Segment: {segment['start_marker']} -> {segment['end_marker']}")
    print(f"Time range: {segment['start_time']:.2f}s - {segment['end_time']:.2f}s")
    print(f"Duration: {segment['duration']:.2f}s")
    
    # The issue might be that when we cut from 682.64s, the resulting segment
    # has a non-zero start time, which causes concatenation problems


def main():
    test_avoid_negative_ts_settings()
    test_smart_cut_timestamps()


if __name__ == "__main__":
    main()
