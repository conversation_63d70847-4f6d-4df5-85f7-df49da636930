#!/usr/bin/env python3
"""
Main application for creating DaVinci Resolve OTIO files from video and dub files.

This application processes video files and their corresponding dub files to create
OpenTimelineIO (OTIO) files that can be imported into DaVinci Resolve with proper
clip linking and picture-in-picture effects for dub files.

<AUTHOR>
@version 2.0
"""
import os
from argparse import ArgumentParser
from pathlib import Path

import questionary
from questionary import Choice

from config_manager import config_manager
from file_processor import (
    find_matching_dub_file, should_process_file, prepare_audio_files,
    process_file_cuts, reencode_dub_file
)
from otio_builder import create_timeline, save_timeline


def get_speed_choices():
    """Get the available speed conversion choices."""
    return [
        Choice(title="No", value="1.000"),
        Choice(title="Re-encode 25", value=["25", "25"]),
        Choice(title="23.976 -> 25", value=["23.976", "25"]),
        Choice(title="23.976 -> 23.950", value=["23.976", "23.950"]),
        Choice(title="24 -> 23.875", value=["24", "23.875"]),
        Choice(title="25 -> 23.976", value=["25", "23.976"]),
        Choice(title="24 -> 23.976", value=["24", "23.976"]),
        Choice(title="50 -> 49.95", value=["50", "49.95"]),
        Choice(title="49.95 -> 50", value=["49.95", "50"]),
        Choice(title="49.95 -> 50 -> 23.950", value=["49.95", "50", "23.976", "23.950"])
    ]


def get_fps_choices():
    """Get the available FPS choices."""
    return ["23.976", "24", "25", "29.97", "30", "50"]


def get_user_preferences(args):
    """Get user preferences for re-encoding."""
    speed_choices = get_speed_choices()
    fps_choices = get_fps_choices()
    
    # Asking the user if they wish to change FPS of the dub files
    if args.reencode:
        dub_file_speed = questionary.select(
            "You want to do speed up the dub files?", 
            choices=speed_choices
        ).ask()
    else:
        dub_file_speed = "1.000"
    
    # If user wants this, give options for output FPS
    dub_output_fps = []
    dub_output_fps_0 = (questionary.select(
        "What should be the output fps of dub file?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) > 2).ask())
    
    dub_output_fps_1 = (questionary.select(
        "What should be the output fps of first round?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) == 2).ask())
    
    dub_output_fps_2 = (questionary.select(
        "What should be the output fps of second round?", 
        choices=fps_choices
    ).skip_if(dub_file_speed == "1.000" or len(dub_file_speed) == 2).ask())
    
    if dub_output_fps_0 is not None:
        dub_output_fps.append(dub_output_fps_0)
    if dub_output_fps_2 is not None:
        dub_output_fps.append(dub_output_fps_1)
        dub_output_fps.append(dub_output_fps_2)
    
    return dub_file_speed, dub_output_fps


def process_file_pair(filename: str, dub_filename: str, args_dir: str, 
                     dub_file_speed, dub_output_fps, silence: bool = False):
    """
    Process a pair of original and dub files.
    
    Args:
        filename: Original filename
        dub_filename: Dub filename
        args_dir: Working directory
        dub_file_speed: Speed conversion parameters
        dub_output_fps: Output FPS parameters
        silence: Whether to use silence detection
    """
    print(f"[info] Processing {filename} with dub {dub_filename}")
    
    dub_file = f'{args_dir}/{dub_filename}'
    org_file = f'{args_dir}/{filename}'
    
    # Prepare audio files
    org_audio_filename, dub_audio_filename = prepare_audio_files(
        org_file, dub_file, args_dir
    )
    
    # Re-encode dub file if needed
    reencode_dub_file(dub_file, dub_file_speed, dub_output_fps, args_dir)
    
    print("[info] Processing into Davinci OTIO file...")
    
    # Build tracks: original video+audio, dub video+audio, with linked clips
    processed_files = {}
    link_index = 0  # Initialize link index counter
    
    # Process original file (video and audio will share the same link_ids)
    orig_video_track, orig_audio_track, link_index = process_file_cuts(
        os.fsencode(args_dir), filename, org_audio_filename, 
        processed_files, silence=silence, link_index=link_index
    )
    
    # Process dub file (video and audio will share the same link_ids)
    dub_video_track, dub_audio_track, link_index = process_file_cuts(
        os.fsencode(args_dir), dub_filename, dub_audio_filename, 
        processed_files, silence=silence, link_index=link_index
    )
    
    # Create timeline with all tracks
    timeline = create_timeline(filename, [
        orig_video_track,
        dub_video_track, 
        orig_audio_track,
        dub_audio_track
    ])
    
    # Save to OTIO file
    otio_path = os.path.join(args_dir, f"{os.path.splitext(filename)[0]}.otio")
    save_timeline(timeline, otio_path)


def run(args):
    """Main application logic."""
    print("[info] Audio Offset Finder - DaVinci Resolve OTIO Generator")

    # Getting directory from config.yml or from command line arguments
    if args.dir is not None:
        args.dir = args.dir.replace("\\", "/")
        print(f"[info] Using directory from command line: {args.dir}")
    else:
        args.dir = config_manager.get_dub_path()
        print(f"[info] Using directory from config.yml: {args.dir}")

    # Verify directory exists
    if not os.path.exists(args.dir):
        print(f"[error] Directory does not exist: {args.dir}")
        print("[info] Please check your config.yml file or specify a valid directory with --dir")
        return

    directory = os.fsencode(args.dir)
    
    # Get user preferences
    dub_file_speed, dub_output_fps = get_user_preferences(args)
    
    # Process all files in directory
    for file in os.listdir(directory):
        filename = os.fsdecode(file)
        
        # Check if file should be processed
        if not should_process_file(filename, args.dir):
            continue
        
        # Find matching dub file
        dub_filename = find_matching_dub_file(filename, directory)
        if not dub_filename:
            print(f"[info] No dub file found for {filename}")
            continue
        
        try:
            process_file_pair(
                filename, dub_filename, args.dir, 
                dub_file_speed, dub_output_fps, args.silence
            )
        except Exception as e:
            print(f"[error] Error processing {filename}: {e}")
            continue


def main():
    """Entry point for the application."""
    parser = ArgumentParser(
        description="Create DaVinci Resolve OTIO files from video and dub files"
    )
    parser.add_argument(
        "--dir",
        help="Directory to process (default: reads 'dub_path' from config.yml)",
        required=False, type=str, default=None
    )
    parser.add_argument(
        "--reencode", 
        action='store_true',
        help="Enable re-encoding options for dub files"
    )
    parser.add_argument(
        "--silence", 
        help="Detect based on silence instead of black screen", 
        action='store_true'
    )
    parser.add_argument(
        "--dub-convert", 
        help="Convert dub file to different FPS", 
        action='store_true'
    )
    
    args = parser.parse_args()
    
    try:
        run(args)
    except KeyboardInterrupt:
        print("\n[info] Operation cancelled by user")
    except Exception as e:
        print(f"[error] Unexpected error: {e}")


if __name__ == '__main__':
    main()
